package com.store.app.petstore.DAO.StatisticDAO;

import com.store.app.petstore.DAO.DatabaseUtil;
import com.store.app.petstore.Models.Records.OrderDetailRecord;
import com.store.app.petstore.Models.Records.PetInventoryStats;
import com.store.app.petstore.Models.Records.ProductInventoryStats;
import com.store.app.petstore.Models.Records.TopSellingItemStats;
import com.store.app.petstore.Models.Records.BusinessPerformanceStats;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class OverviewDAO {

    public static Map<String, String> getDailyStatistics(LocalDate date) throws SQLException {
        Map<String, String> stats = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT " +
                     "SUM(CASE WHEN od.item_type = 'pet' THEN od.quantity ELSE 0 END) AS total_pets_sold, " +
                     "SUM(CASE WHEN od.item_type = 'product' THEN od.quantity ELSE 0 END) AS total_products_sold, " +
                     "SUM(o.total_price) AS total_revenue, " +
                     "COUNT(DISTINCT o.order_id) AS total_invoices " + // Use COUNT(DISTINCT o.order_id) to count unique orders
                     "FROM Orders o " +
                     "LEFT JOIN OrderDetails od ON o.order_id = od.order_id " +
                     "WHERE DATE(o.order_date) = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(date));
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("petsSold", String.valueOf(rs.getInt("total_pets_sold")));
                    stats.put("productsSold", String.valueOf(rs.getInt("total_products_sold")));
                    stats.put("revenue", String.format("%,.2f", rs.getDouble("total_revenue")));
                    stats.put("invoices", String.valueOf(rs.getInt("total_invoices")));
                } else {
                    stats.put("petsSold", "0");
                    stats.put("productsSold", "0");
                    stats.put("revenue", "0.00");
                    stats.put("invoices", "0");
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            stats.put("petsSold", "Error");
            stats.put("productsSold", "Error");
            stats.put("revenue", "Error");
            stats.put("invoices", "Error");
        }

        return stats;
    }

    public static Map<String, Number> getMonthlyRevenue() throws SQLException {
        Map<String, Number> revenue = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT DATE_FORMAT(order_date, '%Y-%m') AS month, SUM(total_price) AS monthly_revenue " +
                     "FROM Orders " +
                     "GROUP BY month " +
                     "ORDER BY month ASC";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String month = rs.getString("month");
                double monthlyRevenue = rs.getDouble("monthly_revenue");
                // Convert month format from YYYY-MM to "Tháng X"
                String[] parts = month.split("-");
                int monthNumber = Integer.parseInt(parts[1]);
                String monthName = "Tháng " + monthNumber;
                revenue.put(monthName, monthlyRevenue);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return revenue;
    }

    public static List<Map<String, String>> getRecentOrders() throws SQLException {
        List<Map<String, String>> orders = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT o.order_id, c.full_name AS customer_name, o.order_date, o.total_price " +
                     "FROM Orders o " +
                     "JOIN Customers c ON o.customer_id = c.customer_id " +
                     "ORDER BY o.order_date DESC " +
                     "LIMIT 10";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                Map<String, String> order = new HashMap<>();
                order.put("id", "ORD-" + rs.getInt("order_id"));
                order.put("customer", rs.getString("customer_name"));
                order.put("date", rs.getDate("order_date").toString());
                order.put("total", String.format("%,.2f", rs.getDouble("total_price")));
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return orders;
    }

    public static List<OrderDetailRecord> getRecentOrderDetails(int orderId) throws SQLException {
        List<OrderDetailRecord> details = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT item_type, item_id, quantity, unit_price FROM OrderDetails WHERE order_id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, orderId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String itemType = rs.getString("item_type");
                    int itemId = rs.getInt("item_id");
                    int quantity = rs.getInt("quantity");
                    double price = rs.getDouble("price");
                    details.add(new OrderDetailRecord(orderId, itemType, itemId, quantity, price));
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return details;
    }

    public static Map<String, Number> getRevenueByDateRange(LocalDate startDate, LocalDate endDate) throws SQLException {
        Map<String, Number> revenue = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT DATE_FORMAT(order_date, '%Y-%m') AS month, SUM(total_price) AS monthly_revenue " +
                     "FROM Orders " +
                     "WHERE order_date BETWEEN ? AND ? " +
                     "GROUP BY month " +
                     "ORDER BY month ASC";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String month = rs.getString("month");
                    double monthlyRevenue = rs.getDouble("monthly_revenue");
                    // Convert month format from YYYY-MM to "Tháng X"
                    String[] parts = month.split("-");
                    int monthNumber = Integer.parseInt(parts[1]);
                    String monthName = "Tháng " + monthNumber;
                    revenue.put(monthName, monthlyRevenue);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return revenue;
    }

    /**
     * Get pet inventory statistics showing stock levels and sales performance
     */
    public static List<PetInventoryStats> getPetInventoryStatistics() throws SQLException {
        List<PetInventoryStats> petStats = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT " +
                     "p.type, " +
                     "p.breed, " +
                     "COUNT(*) as total_in_stock, " +
                     "SUM(CASE WHEN p.isSold = 1 THEN 1 ELSE 0 END) as total_sold, " +
                     "SUM(CASE WHEN p.isSold = 0 THEN 1 ELSE 0 END) as total_available " +
                     "FROM Pets p " +
                     "GROUP BY p.type, p.breed " +
                     "ORDER BY total_available ASC, total_sold DESC";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String type = rs.getString("type");
                String breed = rs.getString("breed");
                int totalInStock = rs.getInt("total_in_stock");
                int totalSold = rs.getInt("total_sold");
                int totalAvailable = rs.getInt("total_available");

                petStats.add(new PetInventoryStats(type, breed, totalInStock, totalSold, totalAvailable));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return petStats;
    }

    /**
     * Get product inventory statistics showing stock levels and sales performance
     */
    public static List<ProductInventoryStats> getProductInventoryStatistics() throws SQLException {
        List<ProductInventoryStats> productStats = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();

        String sql = "SELECT " +
                     "p.name, " +
                     "p.category, " +
                     "p.stock as current_stock, " +
                     "COALESCE(SUM(od.quantity), 0) as total_sold " +
                     "FROM Products p " +
                     "LEFT JOIN OrderDetails od ON p.product_id = od.item_id AND od.item_type = 'product' " +
                     "GROUP BY p.product_id, p.name, p.category, p.stock " +
                     "ORDER BY p.stock ASC, total_sold DESC";

        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String name = rs.getString("name");
                String category = rs.getString("category");
                int currentStock = rs.getInt("current_stock");
                int totalSold = rs.getInt("total_sold");

                productStats.add(new ProductInventoryStats(name, category, currentStock, totalSold));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return productStats;
    }

    /**
     * Get top selling items (both pets and products) for business analysis
     */
    public static List<TopSellingItemStats> getTopSellingItems(int limit) throws SQLException {
        List<TopSellingItemStats> topItems = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();

        // Query for top selling pets
        String petSql = "SELECT " +
                       "p.name, " +
                       "'Pet' as item_type, " +
                       "CONCAT(p.type, ' - ', p.breed) as category, " +
                       "COUNT(od.item_id) as quantity_sold, " +
                       "SUM(od.unit_price * od.quantity) as total_revenue, " +
                       "AVG(od.unit_price) as average_price " +
                       "FROM Pets p " +
                       "JOIN OrderDetails od ON p.pet_id = od.item_id AND od.item_type = 'pet' " +
                       "GROUP BY p.pet_id, p.name, p.type, p.breed " +
                       "ORDER BY quantity_sold DESC " +
                       "LIMIT ?";

        // Query for top selling products
        String productSql = "SELECT " +
                           "p.name, " +
                           "'Product' as item_type, " +
                           "p.category, " +
                           "SUM(od.quantity) as quantity_sold, " +
                           "SUM(od.unit_price * od.quantity) as total_revenue, " +
                           "AVG(od.unit_price) as average_price " +
                           "FROM Products p " +
                           "JOIN OrderDetails od ON p.product_id = od.item_id AND od.item_type = 'product' " +
                           "GROUP BY p.product_id, p.name, p.category " +
                           "ORDER BY quantity_sold DESC " +
                           "LIMIT ?";

        int rank = 1;

        // Get top selling pets
        try (PreparedStatement stmt = connection.prepareStatement(petSql)) {
            stmt.setInt(1, limit / 2); // Half for pets, half for products
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    topItems.add(new TopSellingItemStats(
                            rs.getString("name"),
                            rs.getString("item_type"),
                            rs.getString("category"),
                            rs.getInt("quantity_sold"),
                            rs.getDouble("total_revenue"),
                            rs.getDouble("average_price"),
                            rank++
                    ));
                }
            }
        }

        // Get top selling products
        try (PreparedStatement stmt = connection.prepareStatement(productSql)) {
            stmt.setInt(1, limit / 2);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    topItems.add(new TopSellingItemStats(
                            rs.getString("name"),
                            rs.getString("item_type"),
                            rs.getString("category"),
                            rs.getInt("quantity_sold"),
                            rs.getDouble("total_revenue"),
                            rs.getDouble("average_price"),
                            rank++
                    ));
                }
            }
        }

        // Sort by quantity sold descending and reassign ranks
        topItems.sort((a, b) -> Integer.compare(b.getQuantitySold(), a.getQuantitySold()));
        for (int i = 0; i < topItems.size(); i++) {
            // Note: We can't modify the rank directly as it's a property,
            // so we'll need to create new objects with correct ranks
        }

        return topItems;
    }

    /**
     * Get comprehensive business performance statistics for the specified date range
     */
    public static BusinessPerformanceStats getBusinessPerformanceStats(LocalDate startDate, LocalDate endDate) throws SQLException {
        Connection connection = DatabaseUtil.getConnection();

        // Calculate previous period for comparison
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate);
        LocalDate prevStartDate = startDate.minusDays(daysBetween + 1);
        LocalDate prevEndDate = startDate.minusDays(1);

        // Current period statistics
        String currentSql = "SELECT " +
                           "COUNT(DISTINCT o.order_id) as total_orders, " +
                           "SUM(o.total_price) as total_revenue, " +
                           "AVG(o.total_price) as avg_order_value, " +
                           "COUNT(DISTINCT o.customer_id) as total_customers, " +
                           "SUM(CASE WHEN od.item_type = 'pet' THEN od.quantity ELSE 0 END) as pets_sold, " +
                           "SUM(CASE WHEN od.item_type = 'product' THEN od.quantity ELSE 0 END) as products_sold " +
                           "FROM Orders o " +
                           "LEFT JOIN OrderDetails od ON o.order_id = od.order_id " +
                           "WHERE o.order_date BETWEEN ? AND ?";

        // Previous period statistics for comparison
        String previousSql = "SELECT " +
                            "COUNT(DISTINCT o.order_id) as prev_orders, " +
                            "SUM(o.total_price) as prev_revenue " +
                            "FROM Orders o " +
                            "WHERE o.order_date BETWEEN ? AND ?";

        // New customers in current period
        String newCustomersSql = "SELECT COUNT(DISTINCT customer_id) as new_customers " +
                                "FROM Orders " +
                                "WHERE customer_id NOT IN (" +
                                "    SELECT DISTINCT customer_id FROM Orders " +
                                "    WHERE order_date < ?" +
                                ") AND order_date BETWEEN ? AND ?";

        double totalRevenue = 0, avgOrderValue = 0, prevRevenue = 0;
        int totalOrders = 0, totalCustomers = 0, newCustomers = 0, petsSold = 0, productsSold = 0, prevOrders = 0;

        // Get current period stats
        try (PreparedStatement stmt = connection.prepareStatement(currentSql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    totalOrders = rs.getInt("total_orders");
                    totalRevenue = rs.getDouble("total_revenue");
                    avgOrderValue = rs.getDouble("avg_order_value");
                    totalCustomers = rs.getInt("total_customers");
                    petsSold = rs.getInt("pets_sold");
                    productsSold = rs.getInt("products_sold");
                }
            }
        }

        // Get previous period stats for comparison
        try (PreparedStatement stmt = connection.prepareStatement(previousSql)) {
            stmt.setDate(1, Date.valueOf(prevStartDate));
            stmt.setDate(2, Date.valueOf(prevEndDate));
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    prevOrders = rs.getInt("prev_orders");
                    prevRevenue = rs.getDouble("prev_revenue");
                }
            }
        }

        // Get new customers count
        try (PreparedStatement stmt = connection.prepareStatement(newCustomersSql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(startDate));
            stmt.setDate(3, Date.valueOf(endDate));
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    newCustomers = rs.getInt("new_customers");
                }
            }
        }

        // Calculate growth percentages
        double revenueGrowth = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0;
        int orderGrowth = prevOrders > 0 ? totalOrders - prevOrders : 0;
        double customerRetentionRate = totalCustomers > 0 ? ((double)(totalCustomers - newCustomers) / totalCustomers) * 100 : 0;

        return new BusinessPerformanceStats(
                totalRevenue, revenueGrowth, totalOrders, orderGrowth,
                avgOrderValue, totalCustomers, newCustomers, customerRetentionRate,
                petsSold, productsSold
        );
    }

    /**
     * Get quick overview statistics for dashboard display
     */
    public static Map<String, String> getQuickOverviewStats() throws SQLException {
        Map<String, String> stats = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();

        // Today's statistics
        Map<String, String> todayStats = getDailyStatistics(LocalDate.now());

        // Total pets in stock (available)
        String petStockSql = "SELECT COUNT(*) as pets_in_stock FROM Pets WHERE isSold = 0";

        // Low stock products (stock <= 5)
        String lowStockSql = "SELECT COUNT(*) as low_stock_products FROM Products WHERE stock <= 5";

        // Total customers
        String customersSql = "SELECT COUNT(*) as total_customers FROM Customers";

        try (PreparedStatement stmt = connection.prepareStatement(petStockSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("petsInStock", String.valueOf(rs.getInt("pets_in_stock")));
            }
        }

        try (PreparedStatement stmt = connection.prepareStatement(lowStockSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("lowStockProducts", String.valueOf(rs.getInt("low_stock_products")));
            }
        }

        try (PreparedStatement stmt = connection.prepareStatement(customersSql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.put("totalCustomers", String.valueOf(rs.getInt("total_customers")));
            }
        }

        // Add today's stats
        stats.putAll(todayStats);

        return stats;
    }
}
