package com.store.app.petstore.Models.Records;

import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * Top selling item statistics for identifying best-performing products and pets
 * Essential for business strategy and inventory planning
 */
public class TopSellingItemStats {
    private final SimpleStringProperty itemName;
    private final SimpleStringProperty itemType; // "Pet" or "Product"
    private final SimpleStringProperty category; // Pet type/breed or Product category
    private final SimpleIntegerProperty quantitySold;
    private final SimpleDoubleProperty totalRevenue;
    private final SimpleDoubleProperty averagePrice;
    private final SimpleIntegerProperty rank;

    public TopSellingItemStats(String itemName, String itemType, String category, 
                              int quantitySold, double totalRevenue, double averagePrice, int rank) {
        this.itemName = new SimpleStringProperty(itemName);
        this.itemType = new SimpleStringProperty(itemType);
        this.category = new SimpleStringProperty(category);
        this.quantitySold = new SimpleIntegerProperty(quantitySold);
        this.totalRevenue = new SimpleDoubleProperty(totalRevenue);
        this.averagePrice = new SimpleDoubleProperty(averagePrice);
        this.rank = new SimpleIntegerProperty(rank);
    }

    // Getters
    public String getItemName() {
        return itemName.get();
    }

    public String getItemType() {
        return itemType.get();
    }

    public String getCategory() {
        return category.get();
    }

    public int getQuantitySold() {
        return quantitySold.get();
    }

    public double getTotalRevenue() {
        return totalRevenue.get();
    }

    public double getAveragePrice() {
        return averagePrice.get();
    }

    public int getRank() {
        return rank.get();
    }

    // Property methods for JavaFX binding
    public SimpleStringProperty itemNameProperty() {
        return itemName;
    }

    public SimpleStringProperty itemTypeProperty() {
        return itemType;
    }

    public SimpleStringProperty categoryProperty() {
        return category;
    }

    public SimpleIntegerProperty quantitySoldProperty() {
        return quantitySold;
    }

    public SimpleDoubleProperty totalRevenueProperty() {
        return totalRevenue;
    }

    public SimpleDoubleProperty averagePriceProperty() {
        return averagePrice;
    }

    public SimpleIntegerProperty rankProperty() {
        return rank;
    }

    @Override
    public String toString() {
        return String.format("TopSellingItemStats{rank=%d, name='%s', type='%s', category='%s', sold=%d, revenue=%.2f, avgPrice=%.2f}",
                rank.get(), itemName.get(), itemType.get(), category.get(), quantitySold.get(), totalRevenue.get(), averagePrice.get());
    }
}
