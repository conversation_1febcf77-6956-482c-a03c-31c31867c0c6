package com.store.app.petstore.Models.Records;

import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * Pet inventory statistics for tracking pet stock levels and sales performance
 * Essential for pet shop inventory management and business decisions
 */
public class PetInventoryStats {
    private final SimpleStringProperty petType;
    private final SimpleStringProperty breed;
    private final SimpleIntegerProperty totalInStock;
    private final SimpleIntegerProperty totalSold;
    private final SimpleIntegerProperty totalAvailable;
    private final SimpleStringProperty stockStatus;

    public PetInventoryStats(String petType, String breed, int totalInStock, int totalSold, int totalAvailable) {
        this.petType = new SimpleStringProperty(petType);
        this.breed = new SimpleStringProperty(breed);
        this.totalInStock = new SimpleIntegerProperty(totalInStock);
        this.totalSold = new SimpleIntegerProperty(totalSold);
        this.totalAvailable = new SimpleIntegerProperty(totalAvailable);
        this.stockStatus = new SimpleStringProperty(determineStockStatus(totalAvailable));
    }

    /**
     * Determines stock status based on available quantity
     */
    private String determineStockStatus(int available) {
        if (available == 0) {
            return "Hết hàng";
        } else if (available <= 2) {
            return "Sắp hết";
        } else if (available <= 5) {
            return "Ít hàng";
        } else {
            return "Đủ hàng";
        }
    }

    // Getters
    public String getPetType() {
        return petType.get();
    }

    public String getBreed() {
        return breed.get();
    }

    public int getTotalInStock() {
        return totalInStock.get();
    }

    public int getTotalSold() {
        return totalSold.get();
    }

    public int getTotalAvailable() {
        return totalAvailable.get();
    }

    public String getStockStatus() {
        return stockStatus.get();
    }

    // Property methods for JavaFX binding
    public SimpleStringProperty petTypeProperty() {
        return petType;
    }

    public SimpleStringProperty breedProperty() {
        return breed;
    }

    public SimpleIntegerProperty totalInStockProperty() {
        return totalInStock;
    }

    public SimpleIntegerProperty totalSoldProperty() {
        return totalSold;
    }

    public SimpleIntegerProperty totalAvailableProperty() {
        return totalAvailable;
    }

    public SimpleStringProperty stockStatusProperty() {
        return stockStatus;
    }

    @Override
    public String toString() {
        return String.format("PetInventoryStats{type='%s', breed='%s', inStock=%d, sold=%d, available=%d, status='%s'}",
                petType.get(), breed.get(), totalInStock.get(), totalSold.get(), totalAvailable.get(), stockStatus.get());
    }
}
