package com.store.app.petstore.Models.Records;

import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * Enhanced staff revenue statistics with additional performance metrics
 * for better business insights and staff performance tracking
 */
public class StaffRevenueStats {
    private final SimpleStringProperty staffName;
    private final SimpleDoubleProperty totalRevenue;
    private final SimpleIntegerProperty totalOrders;
    private final SimpleIntegerProperty petsSold;
    private final SimpleIntegerProperty productsSold;
    private final SimpleDoubleProperty averageOrderValue;

    public StaffRevenueStats(String staffName, double totalRevenue) {
        this.staffName = new SimpleStringProperty(staffName);
        this.totalRevenue = new SimpleDoubleProperty(totalRevenue);
        this.totalOrders = new SimpleIntegerProperty(0);
        this.petsSold = new SimpleIntegerProperty(0);
        this.productsSold = new SimpleIntegerProperty(0);
        this.averageOrderValue = new SimpleDoubleProperty(0.0);
    }

    public StaffRevenueStats(String staffName, double totalRevenue, int totalOrders,
                           int petsSold, int productsSold, double averageOrderValue) {
        this.staffName = new SimpleStringProperty(staffName);
        this.totalRevenue = new SimpleDoubleProperty(totalRevenue);
        this.totalOrders = new SimpleIntegerProperty(totalOrders);
        this.petsSold = new SimpleIntegerProperty(petsSold);
        this.productsSold = new SimpleIntegerProperty(productsSold);
        this.averageOrderValue = new SimpleDoubleProperty(averageOrderValue);
    }

    // Getters
    public String getStaffName() {
        return staffName.get();
    }

    public double getTotalRevenue() {
        return totalRevenue.get();
    }

    public int getTotalOrders() {
        return totalOrders.get();
    }

    public int getPetsSold() {
        return petsSold.get();
    }

    public int getProductsSold() {
        return productsSold.get();
    }

    public double getAverageOrderValue() {
        return averageOrderValue.get();
    }

    // Property methods for JavaFX binding
    public SimpleStringProperty staffNameProperty() {
        return staffName;
    }

    public SimpleDoubleProperty totalRevenueProperty() {
        return totalRevenue;
    }

    public SimpleIntegerProperty totalOrdersProperty() {
        return totalOrders;
    }

    public SimpleIntegerProperty petsSoldProperty() {
        return petsSold;
    }

    public SimpleIntegerProperty productsSoldProperty() {
        return productsSold;
    }

    public SimpleDoubleProperty averageOrderValueProperty() {
        return averageOrderValue;
    }

    @Override
    public String toString() {
        return String.format("StaffRevenueStats{staffName='%s', totalRevenue=%.2f, totalOrders=%d, petsSold=%d, productsSold=%d, avgOrderValue=%.2f}",
                staffName.get(), totalRevenue.get(), totalOrders.get(), petsSold.get(), productsSold.get(), averageOrderValue.get());
    }
}
