package com.store.app.petstore.Models.Records;

import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * Business performance statistics providing key performance indicators (KPIs)
 * for comprehensive business analysis and decision making
 */
public class BusinessPerformanceStats {
    private final SimpleDoubleProperty totalRevenue;
    private final SimpleDoubleProperty revenueGrowth; // Percentage growth compared to previous period
    private final SimpleIntegerProperty totalOrders;
    private final SimpleIntegerProperty orderGrowth; // Growth in number of orders
    private final SimpleDoubleProperty averageOrderValue;
    private final SimpleIntegerProperty totalCustomers;
    private final SimpleIntegerProperty newCustomers;
    private final SimpleDoubleProperty customerRetentionRate;
    private final SimpleIntegerProperty totalPetsSold;
    private final SimpleIntegerProperty totalProductsSold;
    private final SimpleStringProperty performanceStatus;

    public BusinessPerformanceStats(double totalRevenue, double revenueGrowth, int totalOrders, int orderGrowth,
                                   double averageOrderValue, int totalCustomers, int newCustomers, 
                                   double customerRetentionRate, int totalPetsSold, int totalProductsSold) {
        this.totalRevenue = new SimpleDoubleProperty(totalRevenue);
        this.revenueGrowth = new SimpleDoubleProperty(revenueGrowth);
        this.totalOrders = new SimpleIntegerProperty(totalOrders);
        this.orderGrowth = new SimpleIntegerProperty(orderGrowth);
        this.averageOrderValue = new SimpleDoubleProperty(averageOrderValue);
        this.totalCustomers = new SimpleIntegerProperty(totalCustomers);
        this.newCustomers = new SimpleIntegerProperty(newCustomers);
        this.customerRetentionRate = new SimpleDoubleProperty(customerRetentionRate);
        this.totalPetsSold = new SimpleIntegerProperty(totalPetsSold);
        this.totalProductsSold = new SimpleIntegerProperty(totalProductsSold);
        this.performanceStatus = new SimpleStringProperty(determinePerformanceStatus(revenueGrowth));
    }

    /**
     * Determines overall performance status based on revenue growth
     */
    private String determinePerformanceStatus(double growth) {
        if (growth >= 20) {
            return "Xuất sắc";
        } else if (growth >= 10) {
            return "Tốt";
        } else if (growth >= 0) {
            return "Ổn định";
        } else if (growth >= -10) {
            return "Cần cải thiện";
        } else {
            return "Cần hành động";
        }
    }

    // Getters
    public double getTotalRevenue() {
        return totalRevenue.get();
    }

    public double getRevenueGrowth() {
        return revenueGrowth.get();
    }

    public int getTotalOrders() {
        return totalOrders.get();
    }

    public int getOrderGrowth() {
        return orderGrowth.get();
    }

    public double getAverageOrderValue() {
        return averageOrderValue.get();
    }

    public int getTotalCustomers() {
        return totalCustomers.get();
    }

    public int getNewCustomers() {
        return newCustomers.get();
    }

    public double getCustomerRetentionRate() {
        return customerRetentionRate.get();
    }

    public int getTotalPetsSold() {
        return totalPetsSold.get();
    }

    public int getTotalProductsSold() {
        return totalProductsSold.get();
    }

    public String getPerformanceStatus() {
        return performanceStatus.get();
    }

    // Property methods for JavaFX binding
    public SimpleDoubleProperty totalRevenueProperty() {
        return totalRevenue;
    }

    public SimpleDoubleProperty revenueGrowthProperty() {
        return revenueGrowth;
    }

    public SimpleIntegerProperty totalOrdersProperty() {
        return totalOrders;
    }

    public SimpleIntegerProperty orderGrowthProperty() {
        return orderGrowth;
    }

    public SimpleDoubleProperty averageOrderValueProperty() {
        return averageOrderValue;
    }

    public SimpleIntegerProperty totalCustomersProperty() {
        return totalCustomers;
    }

    public SimpleIntegerProperty newCustomersProperty() {
        return newCustomers;
    }

    public SimpleDoubleProperty customerRetentionRateProperty() {
        return customerRetentionRate;
    }

    public SimpleIntegerProperty totalPetsSoldProperty() {
        return totalPetsSold;
    }

    public SimpleIntegerProperty totalProductsSoldProperty() {
        return totalProductsSold;
    }

    public SimpleStringProperty performanceStatusProperty() {
        return performanceStatus;
    }

    @Override
    public String toString() {
        return String.format("BusinessPerformanceStats{revenue=%.2f, growth=%.1f%%, orders=%d, avgOrderValue=%.2f, customers=%d, status='%s'}",
                totalRevenue.get(), revenueGrowth.get(), totalOrders.get(), averageOrderValue.get(), totalCustomers.get(), performanceStatus.get());
    }
}
