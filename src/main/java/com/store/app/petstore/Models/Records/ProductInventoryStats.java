package com.store.app.petstore.Models.Records;

import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * Product inventory statistics for tracking product stock levels and sales performance
 * Critical for product inventory management and restocking decisions
 */
public class ProductInventoryStats {
    private final SimpleStringProperty productName;
    private final SimpleStringProperty category;
    private final SimpleIntegerProperty currentStock;
    private final SimpleIntegerProperty totalSold;
    private final SimpleStringProperty stockStatus;
    private final SimpleStringProperty restockAlert;

    public ProductInventoryStats(String productName, String category, int currentStock, int totalSold) {
        this.productName = new SimpleStringProperty(productName);
        this.category = new SimpleStringProperty(category);
        this.currentStock = new SimpleIntegerProperty(currentStock);
        this.totalSold = new SimpleIntegerProperty(totalSold);
        this.stockStatus = new SimpleStringProperty(determineStockStatus(currentStock));
        this.restockAlert = new SimpleStringProperty(determineRestockAlert(currentStock));
    }

    /**
     * Determines stock status based on current stock level
     */
    private String determineStockStatus(int stock) {
        if (stock == 0) {
            return "Hết hàng";
        } else if (stock <= 5) {
            return "Sắp hết";
        } else if (stock <= 20) {
            return "Ít hàng";
        } else {
            return "Đủ hàng";
        }
    }

    /**
     * Determines if restock alert is needed
     */
    private String determineRestockAlert(int stock) {
        if (stock == 0) {
            return "Cần nhập ngay";
        } else if (stock <= 5) {
            return "Cần nhập sớm";
        } else if (stock <= 10) {
            return "Theo dõi";
        } else {
            return "Bình thường";
        }
    }

    // Getters
    public String getProductName() {
        return productName.get();
    }

    public String getCategory() {
        return category.get();
    }

    public int getCurrentStock() {
        return currentStock.get();
    }

    public int getTotalSold() {
        return totalSold.get();
    }

    public String getStockStatus() {
        return stockStatus.get();
    }

    public String getRestockAlert() {
        return restockAlert.get();
    }

    // Property methods for JavaFX binding
    public SimpleStringProperty productNameProperty() {
        return productName;
    }

    public SimpleStringProperty categoryProperty() {
        return category;
    }

    public SimpleIntegerProperty currentStockProperty() {
        return currentStock;
    }

    public SimpleIntegerProperty totalSoldProperty() {
        return totalSold;
    }

    public SimpleStringProperty stockStatusProperty() {
        return stockStatus;
    }

    public SimpleStringProperty restockAlertProperty() {
        return restockAlert;
    }

    @Override
    public String toString() {
        return String.format("ProductInventoryStats{name='%s', category='%s', stock=%d, sold=%d, status='%s', alert='%s'}",
                productName.get(), category.get(), currentStock.get(), totalSold.get(), stockStatus.get(), restockAlert.get());
    }
}
